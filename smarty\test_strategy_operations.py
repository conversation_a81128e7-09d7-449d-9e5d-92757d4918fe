#!/usr/bin/env python3
"""
Comprehensive test script for strategy operations and monitoring metrics.
Tests all aspects of the Smart Trader dashboard functionality.
"""

import asyncio
import aiohttp
import json
import time
import sys
import platform
from datetime import datetime

# Fix for Windows event loop issue
if platform.system() == 'Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

class StrategyOperationsTester:
    def __init__(self, base_url="http://localhost:8082"):
        self.base_url = base_url
        self.session = None
        self.test_results = []

    async def setup_session(self):
        """Setup authenticated session."""
        self.session = aiohttp.ClientSession()

        # Login first
        login_data = {
            "username": "epinnox",
            "password": "securepass123"
        }

        async with self.session.post(f"{self.base_url}/login", data=login_data) as response:
            if response.status == 302:  # Redirect after successful login
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status}")
                return False

    async def test_api_endpoint(self, endpoint, method="GET", data=None, expected_status=200):
        """Test a specific API endpoint."""
        try:
            if method == "GET":
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    status = response.status
                    content = await response.json()
            elif method == "POST":
                async with self.session.post(f"{self.base_url}{endpoint}", json=data) as response:
                    status = response.status
                    content = await response.json()

            success = status == expected_status
            result = {
                "endpoint": endpoint,
                "method": method,
                "status": status,
                "expected": expected_status,
                "success": success,
                "content": content if success else f"Error: {status}"
            }

            self.test_results.append(result)
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} {method} {endpoint} - Status: {status}")

            return content if success else None

        except Exception as e:
            print(f"❌ {method} {endpoint} - Exception: {e}")
            self.test_results.append({
                "endpoint": endpoint,
                "method": method,
                "success": False,
                "error": str(e)
            })
            return None

    async def test_strategy_lifecycle(self, strategy_name):
        """Test complete strategy lifecycle: start -> verify -> stop."""
        print(f"\n🔄 Testing strategy lifecycle for: {strategy_name}")

        # 1. Get initial status
        initial_status = await self.test_api_endpoint("/api/strategy/status")
        if not initial_status:
            return False

        print(f"   Initial status: {initial_status['strategy_status']['strategy_running']}")

        # 2. Start strategy
        start_result = await self.test_api_endpoint(
            "/api/strategy/start",
            method="POST",
            data={"strategy": strategy_name}
        )

        if not start_result or not start_result.get('success'):
            print(f"   ❌ Failed to start {strategy_name}")
            return False

        print(f"   ✅ Started {strategy_name}")

        # 3. Wait and verify it's running
        await asyncio.sleep(2)
        running_status = await self.test_api_endpoint("/api/strategy/status")

        if running_status and running_status['strategy_status']['strategy_running']:
            print(f"   ✅ Verified {strategy_name} is running")

            # 4. Stop strategy
            stop_result = await self.test_api_endpoint("/api/strategy/stop", method="POST")

            if stop_result and stop_result.get('success'):
                print(f"   ✅ Stopped {strategy_name}")

                # 5. Verify it's stopped
                await asyncio.sleep(1)
                stopped_status = await self.test_api_endpoint("/api/strategy/status")

                if stopped_status and not stopped_status['strategy_status']['strategy_running']:
                    print(f"   ✅ Verified {strategy_name} is stopped")
                    return True
                else:
                    print(f"   ❌ {strategy_name} still appears to be running")
                    return False
            else:
                print(f"   ❌ Failed to stop {strategy_name}")
                return False
        else:
            print(f"   ❌ {strategy_name} not running after start")
            return False

    async def test_metrics_system(self):
        """Test monitoring metrics toggling."""
        print(f"\n📊 Testing metrics system")

        # Get current metrics status
        status = await self.test_api_endpoint("/api/strategy/status")
        if not status:
            return False

        current_metrics = status.get('enabled_metrics', {})
        print(f"   Current metrics: {list(current_metrics.keys())}")

        # Test toggling each metric
        test_metrics = ['rsi', 'vwap', 'macd', 'volume_spike', 'orderflow', 'funding_rate']

        for metric in test_metrics:
            if metric in current_metrics:
                # Toggle off then on
                current_state = current_metrics[metric]

                # Toggle to opposite state
                toggle_result = await self.test_api_endpoint(
                    "/api/metrics/toggle",
                    method="POST",
                    data={"metric": metric, "enabled": not current_state}
                )

                if toggle_result and toggle_result.get('success'):
                    print(f"   ✅ Toggled {metric}: {current_state} → {not current_state}")

                    # Toggle back to original state
                    await self.test_api_endpoint(
                        "/api/metrics/toggle",
                        method="POST",
                        data={"metric": metric, "enabled": current_state}
                    )
                    print(f"   ✅ Restored {metric} to original state: {current_state}")
                else:
                    print(f"   ❌ Failed to toggle {metric}")

        return True

    async def test_all_api_endpoints(self):
        """Test all API endpoints for basic functionality."""
        print(f"\n🌐 Testing all API endpoints")

        endpoints = [
            "/api/market-data",
            "/api/signals",
            "/api/trades",
            "/api/stats",
            "/api/orderbook",
            "/api/recent-trades",
            "/api/ai-analysis",
            "/api/market-sentiment",
            "/api/debug",
            "/api/strategy/status"
        ]

        for endpoint in endpoints:
            await self.test_api_endpoint(endpoint)

        return True

    async def run_comprehensive_test(self):
        """Run all tests in sequence."""
        print("🚀 Starting comprehensive strategy operations test")
        print("=" * 60)

        # Setup
        if not await self.setup_session():
            return False

        # Test all API endpoints
        await self.test_all_api_endpoints()

        # Test metrics system
        await self.test_metrics_system()

        # Test strategy lifecycle for key strategies
        strategies_to_test = [
            "Smart Model Integrated",
            "RSI Strategy",
            "Bollinger Bands",
            "Multi-Signal"
        ]

        for strategy in strategies_to_test:
            success = await self.test_strategy_lifecycle(strategy)
            if not success:
                print(f"⚠️  Strategy {strategy} lifecycle test failed")

        # Generate report
        await self.generate_report()

        # Cleanup
        await self.session.close()

        return True

    async def generate_report(self):
        """Generate test results report."""
        print("\n" + "=" * 60)
        print("📋 TEST RESULTS SUMMARY")
        print("=" * 60)

        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r.get('success', False)])

        print(f"Total Tests: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {total_tests - successful_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")

        # Show failed tests
        failed_tests = [r for r in self.test_results if not r.get('success', False)]
        if failed_tests:
            print(f"\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"   - {test['method']} {test['endpoint']}: {test.get('error', 'Unknown error')}")
        else:
            print(f"\n🎉 All tests passed!")

async def main():
    """Main test runner."""
    tester = StrategyOperationsTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
