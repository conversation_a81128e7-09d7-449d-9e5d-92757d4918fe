"""
Live trading demonstration for the enhanced smart-trader system.

This demo showcases the complete live trading system with:
- Real-time model execution
- Performance monitoring
- Web dashboard
- Signal generation
- Risk management
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List

from live_trader import LiveTradingSystem
from monitoring.model_monitor import ModelPerformanceMonitor
# Note: SignalDashboard removed - using unified dashboard instead
from core.feature_store import feature_store
from core.utils import setup_logging

logger = logging.getLogger(__name__)


class LiveTradingDemo:
    """
    Live trading demonstration system.

    Simulates a complete trading session with real-time monitoring,
    model execution, and performance tracking.
    """

    def __init__(self, config_path: str = "config.yaml", demo_duration_minutes: int = 5):
        """
        Initialize the live trading demo.

        Args:
            config_path: Path to configuration file
            demo_duration_minutes: Duration of demo in minutes
        """
        self.config_path = config_path
        self.demo_duration = timedelta(minutes=demo_duration_minutes)
        self.start_time = datetime.now()

        # Demo statistics
        self.demo_stats = {
            "signals_generated": 0,
            "models_executed": 0,
            "predictions_made": 0,
            "alerts_triggered": 0,
            "dashboard_updates": 0,
            "performance_snapshots": []
        }

    async def run_demo(self) -> Dict[str, Any]:
        """Run the live trading demonstration."""
        logger.info("🎬 Starting Live Trading Demo")
        logger.info(f"   Duration: {self.demo_duration.total_seconds() / 60:.1f} minutes")
        logger.info(f"   Dashboard: http://localhost:8081")

        try:
            # Initialize the live trading system
            live_trader = LiveTradingSystem(self.config_path)

            # Start monitoring components
            await live_trader.monitor.start()
            # Note: Using unified dashboard instead

            logger.info("✅ Live trading system initialized")
            logger.info("🌐 Dashboard available at http://localhost:8081")

            # Run demo simulation
            await self._run_demo_simulation(live_trader)

            # Generate demo report
            demo_report = await self._generate_demo_report(live_trader)

            # Note: Unified dashboard continues running

            logger.info("🏁 Live trading demo completed")
            return demo_report

        except Exception as e:
            logger.error(f"Demo failed: {e}")
            raise

    async def _run_demo_simulation(self, live_trader: LiveTradingSystem) -> None:
        """Run the demo simulation."""
        logger.info("🎯 Starting demo simulation")

        end_time = self.start_time + self.demo_duration
        iteration = 0

        while datetime.now() < end_time:
            iteration += 1
            logger.info(f"📊 Demo iteration {iteration}")

            try:
                # Simulate model execution
                await self._simulate_model_execution(live_trader.monitor)

                # Simulate signal generation
                await self._simulate_signal_generation(live_trader.monitor)

                # Take performance snapshot
                await self._take_performance_snapshot(live_trader.monitor)

                # Log progress
                remaining_time = (end_time - datetime.now()).total_seconds()
                logger.info(f"⏱️  Demo progress: {remaining_time:.0f} seconds remaining")

                # Wait before next iteration
                await asyncio.sleep(10)  # 10 seconds between iterations

            except Exception as e:
                logger.error(f"Error in demo iteration {iteration}: {e}")
                await asyncio.sleep(5)

    async def _simulate_model_execution(self, monitor: ModelPerformanceMonitor) -> None:
        """Simulate model execution with realistic predictions."""
        models = ["rsi", "vwap_deviation", "garch_volatility", "funding_momentum", "orderflow"]

        for i, model_name in enumerate(models):
            try:
                # Simulate prediction latency
                latency = 50 + (i % 5) * 20  # 50-130ms

                # Generate realistic prediction
                prediction = self._generate_realistic_prediction(model_name)

                # Record prediction
                await monitor.record_prediction(
                    model_name,
                    prediction,
                    latency,
                    {"symbol": "BTC-USDT", "timestamp": datetime.now().isoformat()}
                )

                self.demo_stats["predictions_made"] += 1
                self.demo_stats["models_executed"] += 1

                logger.debug(f"🤖 {model_name} prediction: {prediction.get('signal_strength', 0):.2f}")

            except Exception as e:
                logger.error(f"Error simulating {model_name}: {e}")

    async def _simulate_signal_generation(self, monitor: ModelPerformanceMonitor) -> None:
        """Simulate trading signal generation."""
        strategies = ["smart_integrated", "multi_signal", "ensemble"]

        for strategy in strategies:
            try:
                # Generate realistic signal
                signal = self._generate_realistic_signal(strategy)

                # Record signal
                await monitor.record_signal(strategy, signal)

                self.demo_stats["signals_generated"] += 1

                logger.info(f"📈 {strategy}: {signal['action']} signal (strength: {signal['score']:.2f})")

            except Exception as e:
                logger.error(f"Error simulating signal for {strategy}: {e}")

    async def _take_performance_snapshot(self, monitor: ModelPerformanceMonitor) -> None:
        """Take a performance snapshot."""
        try:
            summary = await monitor.get_performance_summary()

            snapshot = {
                "timestamp": datetime.now().isoformat(),
                "total_predictions": summary.get("models", {}).get("total_predictions", 0),
                "total_signals": summary.get("signals", {}).get("total_signals", 0),
                "active_models": summary.get("models", {}).get("active_models", 0),
                "avg_latency": summary.get("models", {}).get("avg_latency_ms", 0),
                "active_alerts": len(summary.get("active_alerts", []))
            }

            self.demo_stats["performance_snapshots"].append(snapshot)
            self.demo_stats["dashboard_updates"] += 1

            # Check for alerts
            if snapshot["active_alerts"] > 0:
                self.demo_stats["alerts_triggered"] += 1

        except Exception as e:
            logger.error(f"Error taking performance snapshot: {e}")

    def _generate_realistic_prediction(self, model_name: str) -> Dict[str, Any]:
        """Generate realistic model prediction."""
        import random

        base_predictions = {
            "rsi": {
                "rsi": random.uniform(30, 70),
                "signal_strength": random.uniform(-0.5, 0.5),
                "confidence": random.uniform(0.6, 0.9)
            },
            "vwap_deviation": {
                "z_score": random.uniform(-2, 2),
                "signal_strength": random.uniform(-0.8, 0.8),
                "confidence": random.uniform(0.5, 0.85)
            },
            "garch_volatility": {
                "volatility": random.uniform(0.2, 0.6),
                "regime": random.choice(["LOW", "NORMAL", "HIGH"]),
                "signal_strength": random.uniform(-0.3, 0.3),
                "confidence": random.uniform(0.7, 0.95)
            },
            "funding_momentum": {
                "funding_rate": random.uniform(-0.01, 0.01),
                "momentum": random.uniform(-0.5, 0.5),
                "signal_strength": random.uniform(-0.6, 0.6),
                "confidence": random.uniform(0.6, 0.8)
            },
            "orderflow": {
                "delta_price_60s": random.uniform(-100, 100),
                "direction": random.choice(["up", "down", "neutral"]),
                "signal_strength": random.uniform(-0.7, 0.7),
                "confidence": random.uniform(0.5, 0.9)
            }
        }

        return base_predictions.get(model_name, {
            "signal_strength": random.uniform(-0.5, 0.5),
            "confidence": random.uniform(0.5, 0.8)
        })

    def _generate_realistic_signal(self, strategy: str) -> Dict[str, Any]:
        """Generate realistic trading signal."""
        import random

        actions = ["BUY", "SELL"]
        action = random.choice(actions)

        # Generate signal strength based on strategy
        if strategy == "smart_integrated":
            score = random.uniform(0.6, 0.95)  # High confidence strategy
        elif strategy == "multi_signal":
            score = random.uniform(0.5, 0.8)   # Medium confidence
        else:
            score = random.uniform(0.4, 0.7)   # Conservative

        return {
            "symbol": "BTC-USDT",
            "action": action,
            "score": score,
            "timestamp": datetime.now().isoformat(),
            "rationale": f"{strategy} {action} signal based on model ensemble"
        }

    async def _generate_demo_report(self, live_trader: LiveTradingSystem) -> Dict[str, Any]:
        """Generate comprehensive demo report."""
        logger.info("📊 Generating demo report")

        try:
            # Get final performance summary
            final_summary = await live_trader.monitor.get_performance_summary()

            # Calculate demo statistics
            demo_duration_seconds = (datetime.now() - self.start_time).total_seconds()

            report = {
                "demo_info": {
                    "start_time": self.start_time.isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration_seconds": demo_duration_seconds,
                    "duration_minutes": demo_duration_seconds / 60
                },
                "demo_statistics": self.demo_stats,
                "performance_summary": final_summary,
                "key_metrics": {
                    "predictions_per_minute": self.demo_stats["predictions_made"] / (demo_duration_seconds / 60),
                    "signals_per_minute": self.demo_stats["signals_generated"] / (demo_duration_seconds / 60),
                    "models_active": final_summary.get("models", {}).get("active_models", 0),
                    "avg_prediction_latency": final_summary.get("models", {}).get("avg_latency_ms", 0),
                    "alert_rate": self.demo_stats["alerts_triggered"] / max(self.demo_stats["dashboard_updates"], 1)
                },
                "system_health": {
                    "status": "HEALTHY" if final_summary.get("models", {}).get("active_models", 0) > 0 else "DEGRADED",
                    "uptime_hours": final_summary.get("uptime_hours", 0),
                    "active_alerts": final_summary.get("active_alerts", [])
                }
            }

            # Save report
            report_file = f"demo_reports/live_trading_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            import os
            os.makedirs("demo_reports", exist_ok=True)

            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            logger.info(f"📄 Demo report saved to {report_file}")

            # Log summary
            self._log_demo_summary(report)

            return report

        except Exception as e:
            logger.error(f"Error generating demo report: {e}")
            return {"error": str(e)}

    def _log_demo_summary(self, report: Dict[str, Any]) -> None:
        """Log demo summary."""
        logger.info("🎬 Live Trading Demo Summary:")
        logger.info(f"   Duration: {report['demo_info']['duration_minutes']:.1f} minutes")
        logger.info(f"   Predictions Made: {report['demo_statistics']['predictions_made']}")
        logger.info(f"   Signals Generated: {report['demo_statistics']['signals_generated']}")
        logger.info(f"   Models Executed: {report['demo_statistics']['models_executed']}")
        logger.info(f"   Alerts Triggered: {report['demo_statistics']['alerts_triggered']}")
        logger.info(f"   Predictions/Min: {report['key_metrics']['predictions_per_minute']:.1f}")
        logger.info(f"   Signals/Min: {report['key_metrics']['signals_per_minute']:.1f}")
        logger.info(f"   Avg Latency: {report['key_metrics']['avg_prediction_latency']:.1f}ms")
        logger.info(f"   System Status: {report['system_health']['status']}")


async def main():
    """Run the live trading demo."""
    setup_logging(level="INFO")

    print("🚀 Enhanced Smart-Trader Live Demo")
    print("=" * 50)
    print("This demo showcases:")
    print("• Real-time model execution")
    print("• Performance monitoring")
    print("• Web dashboard (http://localhost:8081)")
    print("• Signal generation")
    print("• Alert system")
    print("=" * 50)

    # Get demo duration from user
    try:
        duration = input("Enter demo duration in minutes (default: 2): ").strip()
        duration = int(duration) if duration else 2
    except ValueError:
        duration = 2

    print(f"\n🎬 Starting {duration}-minute live trading demo...")
    print("💡 Open http://localhost:8081 in your browser to view the dashboard")

    # Run demo
    demo = LiveTradingDemo(demo_duration_minutes=duration)
    report = await demo.run_demo()

    print("\n✅ Demo completed successfully!")
    print(f"📊 Generated {report.get('demo_statistics', {}).get('predictions_made', 0)} predictions")
    print(f"📈 Generated {report.get('demo_statistics', {}).get('signals_generated', 0)} signals")
    print("📄 Check demo_reports/ for detailed results")


if __name__ == "__main__":
    asyncio.run(main())
