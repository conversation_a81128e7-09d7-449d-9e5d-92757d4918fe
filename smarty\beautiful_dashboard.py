#!/usr/bin/env python3
"""
Beautiful Live Trading Dashboard
A modern, professional dashboard for the Smart Trader system.
"""

import asyncio
import json
import logging
import sqlite3
import subprocess
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Set
import aiohttp
from aiohttp import web, WSMsgType
import aiohttp_session
from aiohttp_session.cookie_storage import EncryptedCookieStorage
import base64
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BeautifulDashboard:
    """Beautiful Live Trading Dashboard with modern UI and real-time updates."""

    def __init__(self, db_path: str = "data/bus.db", port: int = 8082):
        self.db_path = db_path
        self.port = port
        self.websockets: Set[web.WebSocketResponse] = set()
        self.app = None

        # Dashboard state
        self.current_symbol = "BTC-USDT"
        self.current_strategy = "Smart Model Integrated"
        self.available_symbols = ["BTC-USDT", "ETH-USDT", "SOL-USDT", "ADA-USDT"]
        self.available_strategies = [
            "Smart Model Integrated",
            "RSI Strategy",
            "Bollinger Bands",
            "VWAP Strategy",
            "Multi-Signal"
        ]

        # Strategy management
        self.strategy_processes = {}
        self.strategy_running = False
        self.strategy_commands = {
            "Smart Model Integrated": "python orchestrator.py",
            "RSI Strategy": "python demo_live_trading.py --strategy=rsi",
            "Bollinger Bands": "python demo_live_trading.py --strategy=bollinger",
            "VWAP Strategy": "python demo_live_trading.py --strategy=vwap",
            "Multi-Signal": "python demo_live_trading.py --strategy=multi"
        }

        # Metrics configuration
        self.enabled_metrics = {
            "rsi": True,
            "vwap": True,
            "volume_spike": False,
            "macd": True,
            "bollinger": True,
            "support_resistance": False,
            "momentum": True,
            "volatility": True
        }

        # Performance tracking
        self.strategy_stats = {
            strategy: {
                "signals_generated": 0,
                "avg_confidence": 0.0,
                "last_signal_time": None,
                "status": "stopped",
                "uptime": 0
            }
            for strategy in self.available_strategies
        }

        # Data cache
        self.data_cache = {
            "market_data": {},
            "signals": [],
            "trades": [],
            "ai_analysis": {},
            "last_update": None
        }

    async def init_app(self):
        """Initialize the web application."""
        self.app = web.Application()

        # Skip session storage for now - use simple authentication
        # aiohttp_session.setup(self.app, SimpleCookieStorage())

        # Setup routes
        self.setup_routes()

        logger.info("🌐 Beautiful Dashboard initialized")

    async def start_background_tasks(self):
        """Start background tasks after server is running."""
        logger.info("🔄 Starting background tasks...")
        asyncio.create_task(self.background_updater())
        asyncio.create_task(self.data_collector())

    def setup_routes(self):
        """Setup web routes."""
        # Main pages
        self.app.router.add_get('/', self.redirect_to_login)
        self.app.router.add_get('/login', self.login_page)
        self.app.router.add_post('/login', self.handle_login)
        self.app.router.add_get('/dashboard', self.dashboard_page)
        self.app.router.add_get('/logout', self.handle_logout)

        # WebSocket
        self.app.router.add_get('/ws', self.websocket_handler)

        # API endpoints
        self.app.router.add_get('/api/market-data', self.api_market_data)
        self.app.router.add_get('/api/signals', self.api_signals)
        self.app.router.add_get('/api/trades', self.api_trades)
        self.app.router.add_get('/api/ai-analysis', self.api_ai_analysis)
        self.app.router.add_get('/api/strategy/status', self.api_strategy_status)
        self.app.router.add_post('/api/strategy/start', self.api_strategy_start)
        self.app.router.add_post('/api/strategy/stop', self.api_strategy_stop)
        self.app.router.add_post('/api/symbol/select', self.api_symbol_select)
        self.app.router.add_post('/api/metrics/toggle', self.api_metrics_toggle)

    async def redirect_to_login(self, request):
        """Redirect root to login."""
        return web.HTTPFound('/login')

    async def login_page(self, request):
        """Serve the login page."""
        # Simple approach - just serve login page
        html = self.get_login_html()
        return web.Response(text=html, content_type='text/html')

    async def handle_login(self, request):
        """Handle login form submission."""
        data = await request.post()
        username = data.get('username', '')
        password = data.get('password', '')

        # Simple authentication (in production, use proper auth)
        if username == 'epinnox' and password == 'securepass123':
            logger.info(f"✅ Successful login for user: {username}")
            return web.HTTPFound('/dashboard')
        else:
            logger.warning(f"❌ Failed login attempt for user: {username}")
            return web.HTTPFound('/login?error=1')

    async def handle_logout(self, request):
        """Handle logout."""
        return web.HTTPFound('/login')

    async def dashboard_page(self, request):
        """Serve the main dashboard page."""
        # For now, serve dashboard directly (in production, add proper auth)
        html = self.get_dashboard_html()
        return web.Response(text=html, content_type='text/html')

    def get_login_html(self):
        """Generate beautiful login page HTML."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epinnox Trading Dashboard - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 2.5em;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .error {
            color: #e74c3c;
            margin-top: 15px;
            font-size: 14px;
        }

        .footer {
            margin-top: 30px;
            color: #888;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">⚡ EPINNOX</div>
        <div class="subtitle">AI Trading Dashboard</div>

        <form method="post" action="/login">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="login-btn">Sign In</button>
        </form>

        <div class="footer">
            Secure access to your trading dashboard
        </div>
    </div>

    <script>
        // Check for error parameter
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('error')) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = 'Invalid username or password';
            document.querySelector('form').appendChild(errorDiv);
        }
    </script>
</body>
</html>
        """

    def get_dashboard_html(self):
        """Generate beautiful dashboard HTML."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epinnox Trading Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0e1a;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #1a1f3a 0%, #2d3561 100%);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            font-size: 1.8em;
            font-weight: bold;
            background: linear-gradient(135deg, #00d4ff, #5b73ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-controls {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .symbol-selector, .strategy-selector {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px 15px;
            color: white;
            font-size: 14px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(0, 212, 255, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Main Layout */
        .dashboard-container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 20px;
            padding: 20px;
            min-height: calc(100vh - 80px);
        }

        /* Cards */
        .card {
            background: linear-gradient(135deg, #1e2139 0%, #252a4a 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .card-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 15px;
            color: #00d4ff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Price Display */
        .price-display {
            grid-column: 1 / 3;
            text-align: center;
        }

        .current-price {
            font-size: 3em;
            font-weight: bold;
            background: linear-gradient(135deg, #00ff88, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .price-change {
            font-size: 1.2em;
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .change-positive {
            color: #00ff88;
        }

        .change-negative {
            color: #ff4757;
        }

        /* Strategy Controls */
        .strategy-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .control-group {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-primary {
            background: linear-gradient(135deg, #00ff88, #00d4ff);
            color: #0a0e1a;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff4757, #ff6b7a);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        /* AI Analysis */
        .ai-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .metric {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9em;
            color: #888;
        }

        /* Trading Activity */
        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-type {
            font-weight: 600;
        }

        .activity-time {
            font-size: 0.8em;
            color: #888;
        }

        /* Performance Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            color: #888;
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00d4ff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">⚡ EPINNOX</div>
        <div class="header-controls">
            <select class="symbol-selector" id="symbolSelector">
                <option value="BTC-USDT">BTC-USDT</option>
                <option value="ETH-USDT">ETH-USDT</option>
                <option value="SOL-USDT">SOL-USDT</option>
                <option value="ADA-USDT">ADA-USDT</option>
            </select>

            <select class="strategy-selector" id="strategySelector">
                <option value="Smart Model Integrated">Smart Model Integrated</option>
                <option value="RSI Strategy">RSI Strategy</option>
                <option value="Bollinger Bands">Bollinger Bands</option>
                <option value="VWAP Strategy">VWAP Strategy</option>
                <option value="Multi-Signal">Multi-Signal</option>
            </select>

            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="connectionStatus">Connected</span>
            </div>

            <a href="/logout" style="color: #888; text-decoration: none;">Logout</a>
        </div>
    </div>

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Price Display -->
        <div class="card price-display">
            <div class="card-title">📈 Live Price</div>
            <div class="current-price" id="currentPrice">$0.00</div>
            <div class="price-change">
                <span id="priceChange24h" class="change-positive">+0.00%</span>
                <span id="priceChangeValue" class="change-positive">+$0.00</span>
            </div>
        </div>

        <!-- Strategy Controls -->
        <div class="card">
            <div class="card-title">🎯 Strategy Control</div>
            <div class="strategy-controls">
                <div class="control-group">
                    <button class="btn btn-primary" id="startBtn">Start Strategy</button>
                    <button class="btn btn-danger" id="stopBtn">Stop Strategy</button>
                </div>
                <div id="strategyStatus" style="text-align: center; margin-top: 10px; color: #888;">
                    Strategy Stopped
                </div>
            </div>
        </div>

        <!-- AI Analysis -->
        <div class="card">
            <div class="card-title">🤖 AI Analysis</div>
            <div class="ai-metrics">
                <div class="metric">
                    <div class="metric-value" id="rsiValue">--</div>
                    <div class="metric-label">RSI</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="trendValue">--</div>
                    <div class="metric-label">Trend</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="volatilityValue">--</div>
                    <div class="metric-label">Volatility</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="signalStrength">--</div>
                    <div class="metric-label">Signal</div>
                </div>
            </div>
        </div>

        <!-- Trading Activity -->
        <div class="card">
            <div class="card-title">📊 Trading Activity</div>
            <div class="activity-list" id="activityList">
                <div class="activity-item">
                    <div>
                        <div class="activity-type">System Started</div>
                        <div class="activity-time">Just now</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Stats -->
        <div class="card">
            <div class="card-title">📈 Performance</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalSignals">0</div>
                    <div class="stat-label">Signals</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgConfidence">0%</div>
                    <div class="stat-label">Confidence</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="uptime">0h 0m</div>
                    <div class="stat-label">Uptime</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="dataPoints">0</div>
                    <div class="stat-label">Data Points</div>
                </div>
            </div>
        </div>

        <!-- Market Overview -->
        <div class="card">
            <div class="card-title">🌐 Market Overview</div>
            <div class="overview-grid">
                <div class="overview-item">
                    <div class="overview-title">Volume 24h</div>
                    <div class="overview-value" id="volume24h">$0</div>
                </div>
                <div class="overview-item">
                    <div class="overview-title">Market Cap</div>
                    <div class="overview-value" id="marketCap">$0</div>
                </div>
                <div class="overview-item">
                    <div class="overview-title">Active Trades</div>
                    <div class="overview-value" id="activeTrades">0</div>
                </div>
                <div class="overview-item">
                    <div class="overview-title">System Status</div>
                    <div class="overview-value" style="color: #00ff88;">Online</div>
                </div>
            </div>
        </div>

        <!-- Live Price Chart -->
        <div class="card" style="grid-column: 1 / 4; height: 400px;">
            <div class="card-title">📈 Live Price Chart & Signals</div>
            <div style="position: relative; height: 350px; background: rgba(255, 255, 255, 0.05); border-radius: 10px; padding: 20px;">
                <canvas id="priceChart" style="width: 100%; height: 100%;"></canvas>
                <div id="chartLegend" style="position: absolute; top: 10px; right: 10px; background: rgba(0, 0, 0, 0.7); padding: 10px; border-radius: 5px; font-size: 12px;">
                    <div style="color: #00ff88;">🟢 Buy Signals</div>
                    <div style="color: #ff4757;">🔴 Sell Signals</div>
                    <div style="color: #00d4ff;">📊 Price Line</div>
                </div>
            </div>
        </div>

        <!-- Advanced Metrics -->
        <div class="card" style="grid-column: 1 / 3;">
            <div class="card-title">⚡ Advanced Metrics</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                <div class="metric">
                    <div class="metric-value" id="macdValue">--</div>
                    <div class="metric-label">MACD</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="bollingerValue">--</div>
                    <div class="metric-label">Bollinger</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="volumeProfile">--</div>
                    <div class="metric-label">Volume</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="supportLevel">--</div>
                    <div class="metric-label">Support</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="resistanceLevel">--</div>
                    <div class="metric-label">Resistance</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="momentumValue">--</div>
                    <div class="metric-label">Momentum</div>
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="card">
            <div class="card-title">🔧 System Health</div>
            <div style="display: flex; flex-direction: column; gap: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>Data Feed</span>
                    <span id="dataFeedStatus" style="color: #00ff88;">●</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>AI Models</span>
                    <span id="aiModelsStatus" style="color: #00ff88;">●</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>Strategy Engine</span>
                    <span id="strategyEngineStatus" style="color: #00ff88;">●</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>WebSocket</span>
                    <span id="websocketStatus" style="color: #00ff88;">●</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>Database</span>
                    <span id="databaseStatus" style="color: #00ff88;">●</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection
        let ws = null;
        let reconnectInterval = null;

        // Dashboard state
        let currentSymbol = 'BTC-USDT';
        let currentStrategy = 'Smart Model Integrated';
        let startTime = Date.now();

        // Chart data
        let priceChart = null;
        let priceData = [];
        let signalData = [];
        let maxDataPoints = 50;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            initializeWebSocket();
            setupEventListeners();
            startDataUpdates();
            updateUptime();
            updateSystemHealth();
        });

        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                console.log('✅ WebSocket connected');
                document.getElementById('connectionStatus').textContent = 'Connected';
                document.querySelector('.status-dot').style.background = '#00ff88';

                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            ws.onclose = function() {
                console.log('❌ WebSocket disconnected');
                document.getElementById('connectionStatus').textContent = 'Disconnected';
                document.querySelector('.status-dot').style.background = '#ff4757';

                // Attempt to reconnect
                if (!reconnectInterval) {
                    reconnectInterval = setInterval(initializeWebSocket, 5000);
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        function handleWebSocketMessage(data) {
            if (data.type === 'market_update') {
                updateMarketData(data.data);
            } else if (data.type === 'signal_update') {
                updateSignalData(data.data);
            } else if (data.type === 'ai_analysis') {
                updateAIAnalysis(data.data);
            } else if (data.type === 'strategy_status') {
                updateStrategyStatus(data.data);
            }
        }

        function setupEventListeners() {
            // Symbol selector
            document.getElementById('symbolSelector').addEventListener('change', function() {
                const newSymbol = this.value;
                if (newSymbol !== currentSymbol) {
                    changeSymbol(newSymbol);
                }
            });

            // Strategy selector
            document.getElementById('strategySelector').addEventListener('change', function() {
                currentStrategy = this.value;
            });

            // Strategy control buttons
            document.getElementById('startBtn').addEventListener('click', startStrategy);
            document.getElementById('stopBtn').addEventListener('click', stopStrategy);
        }

        async function changeSymbol(symbol) {
            try {
                const response = await fetch('/api/symbol/select', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ symbol: symbol })
                });

                if (response.ok) {
                    currentSymbol = symbol;
                    addActivity(`Symbol changed to ${symbol}`, 'info');
                } else {
                    console.error('Failed to change symbol');
                }
            } catch (error) {
                console.error('Error changing symbol:', error);
            }
        }

        async function startStrategy() {
            try {
                const response = await fetch('/api/strategy/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ strategy: currentStrategy })
                });

                const result = await response.json();
                if (result.success) {
                    document.getElementById('strategyStatus').textContent = `${currentStrategy} Running`;
                    document.getElementById('strategyStatus').style.color = '#00ff88';
                    addActivity(`Started ${currentStrategy}`, 'success');
                } else {
                    addActivity(`Failed to start strategy: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Error starting strategy:', error);
                addActivity('Error starting strategy', 'error');
            }
        }

        async function stopStrategy() {
            try {
                const response = await fetch('/api/strategy/stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                if (result.success) {
                    document.getElementById('strategyStatus').textContent = 'Strategy Stopped';
                    document.getElementById('strategyStatus').style.color = '#888';
                    addActivity('Strategy stopped', 'info');
                } else {
                    addActivity(`Failed to stop strategy: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Error stopping strategy:', error);
                addActivity('Error stopping strategy', 'error');
            }
        }

        function initializeChart() {
            const canvas = document.getElementById('priceChart');
            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            priceChart = {
                canvas: canvas,
                ctx: ctx,
                width: canvas.width,
                height: canvas.height,
                padding: 40
            };

            // Initial chart draw
            drawChart();
        }

        function drawChart() {
            if (!priceChart) return;

            const { ctx, width, height, padding } = priceChart;

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            // Draw background grid
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;

            // Vertical grid lines
            for (let i = 0; i <= 10; i++) {
                const x = padding + (width - 2 * padding) * i / 10;
                ctx.beginPath();
                ctx.moveTo(x, padding);
                ctx.lineTo(x, height - padding);
                ctx.stroke();
            }

            // Horizontal grid lines
            for (let i = 0; i <= 5; i++) {
                const y = padding + (height - 2 * padding) * i / 5;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(width - padding, y);
                ctx.stroke();
            }

            // Draw price line
            if (priceData.length > 1) {
                ctx.strokeStyle = '#00d4ff';
                ctx.lineWidth = 2;
                ctx.beginPath();

                const minPrice = Math.min(...priceData);
                const maxPrice = Math.max(...priceData);
                const priceRange = maxPrice - minPrice || 1;

                priceData.forEach((price, index) => {
                    const x = padding + (width - 2 * padding) * index / (maxDataPoints - 1);
                    const y = height - padding - (height - 2 * padding) * (price - minPrice) / priceRange;

                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });

                ctx.stroke();

                // Draw signal points
                signalData.forEach((signal, index) => {
                    if (signal && index < priceData.length) {
                        const x = padding + (width - 2 * padding) * index / (maxDataPoints - 1);
                        const y = height - padding - (height - 2 * padding) * (priceData[index] - minPrice) / priceRange;

                        ctx.fillStyle = signal === 'BUY' ? '#00ff88' : '#ff4757';
                        ctx.beginPath();
                        ctx.arc(x, y, 4, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                });
            }

            // Draw current price label
            if (priceData.length > 0) {
                const currentPrice = priceData[priceData.length - 1];
                ctx.fillStyle = '#00d4ff';
                ctx.font = '12px Arial';
                ctx.fillText(`$${currentPrice.toFixed(2)}`, width - 80, 30);
            }
        }

        function updateMarketData(data) {
            if (data.price) {
                const price = parseFloat(data.price);
                document.getElementById('currentPrice').textContent = `$${price.toLocaleString()}`;

                // Update chart data
                priceData.push(price);
                if (priceData.length > maxDataPoints) {
                    priceData.shift();
                    signalData.shift();
                }

                // Redraw chart
                drawChart();
            }

            if (data.change_24h) {
                const change = parseFloat(data.change_24h);
                const changeElement = document.getElementById('priceChange24h');
                changeElement.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                changeElement.className = change >= 0 ? 'change-positive' : 'change-negative';
            }

            if (data.volume_24h) {
                document.getElementById('volume24h').textContent = `$${formatLargeNumber(data.volume_24h)}`;
            }
        }

        function updateAIAnalysis(data) {
            if (data.rsi !== undefined) {
                document.getElementById('rsiValue').textContent = data.rsi.toFixed(1);
            }

            if (data.trend) {
                document.getElementById('trendValue').textContent = data.trend;
            }

            if (data.volatility) {
                document.getElementById('volatilityValue').textContent = data.volatility;
            }

            if (data.signal_strength !== undefined) {
                document.getElementById('signalStrength').textContent = `${(data.signal_strength * 100).toFixed(0)}%`;
            }

            // Update advanced metrics with simulated data
            updateAdvancedMetrics(data);

            // Add signal to chart if direction is provided
            if (data.direction && data.direction !== 'HOLD') {
                signalData.push(data.direction);
                if (signalData.length > maxDataPoints) {
                    signalData.shift();
                }
            } else {
                signalData.push(null);
                if (signalData.length > maxDataPoints) {
                    signalData.shift();
                }
            }
        }

        function updateAdvancedMetrics(data) {
            // Simulate advanced metrics based on AI analysis
            const rsi = data.rsi || 50;
            const signalStrength = data.signal_strength || 0.5;

            // MACD simulation
            const macd = ((rsi - 50) / 50 * signalStrength).toFixed(3);
            document.getElementById('macdValue').textContent = macd;

            // Bollinger Bands simulation
            const bollinger = rsi > 70 ? 'Upper' : rsi < 30 ? 'Lower' : 'Middle';
            document.getElementById('bollingerValue').textContent = bollinger;

            // Volume Profile simulation
            const volume = (signalStrength * 100).toFixed(0) + '%';
            document.getElementById('volumeProfile').textContent = volume;

            // Support/Resistance levels (simulated)
            const currentPrice = priceData.length > 0 ? priceData[priceData.length - 1] : 50000;
            const support = (currentPrice * 0.98).toFixed(0);
            const resistance = (currentPrice * 1.02).toFixed(0);
            document.getElementById('supportLevel').textContent = `$${support}`;
            document.getElementById('resistanceLevel').textContent = `$${resistance}`;

            // Momentum simulation
            const momentum = data.trend === 'Bullish' ? 'Strong' : data.trend === 'Bearish' ? 'Weak' : 'Neutral';
            document.getElementById('momentumValue').textContent = momentum;
        }

        function updateSystemHealth() {
            // Simulate system health indicators
            const indicators = ['dataFeedStatus', 'aiModelsStatus', 'strategyEngineStatus', 'websocketStatus', 'databaseStatus'];

            indicators.forEach(indicator => {
                const element = document.getElementById(indicator);
                if (element) {
                    // Simulate occasional yellow/red status
                    const status = Math.random() > 0.1 ? 'green' : Math.random() > 0.5 ? 'yellow' : 'red';
                    const colors = {
                        'green': '#00ff88',
                        'yellow': '#ffa502',
                        'red': '#ff4757'
                    };
                    element.style.color = colors[status];
                }
            });

            // Update WebSocket status based on actual connection
            const wsStatus = document.getElementById('websocketStatus');
            if (wsStatus) {
                wsStatus.style.color = ws && ws.readyState === WebSocket.OPEN ? '#00ff88' : '#ff4757';
            }
        }

        function updateStrategyStatus(data) {
            if (data.signals_generated !== undefined) {
                document.getElementById('totalSignals').textContent = data.signals_generated;
            }

            if (data.avg_confidence !== undefined) {
                document.getElementById('avgConfidence').textContent = `${(data.avg_confidence * 100).toFixed(0)}%`;
            }
        }

        function addActivity(message, type = 'info') {
            const activityList = document.getElementById('activityList');
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';

            const now = new Date();
            const timeString = now.toLocaleTimeString();

            let icon = '📊';
            if (type === 'success') icon = '✅';
            else if (type === 'error') icon = '❌';
            else if (type === 'warning') icon = '⚠️';

            activityItem.innerHTML = `
                <div>
                    <div class="activity-type">${icon} ${message}</div>
                    <div class="activity-time">${timeString}</div>
                </div>
            `;

            activityList.insertBefore(activityItem, activityList.firstChild);

            // Keep only last 10 items
            while (activityList.children.length > 10) {
                activityList.removeChild(activityList.lastChild);
            }
        }

        function startDataUpdates() {
            // Update data every 2 seconds
            setInterval(async function() {
                try {
                    // Fetch AI analysis
                    const aiResponse = await fetch('/api/ai-analysis');
                    if (aiResponse.ok) {
                        const aiData = await aiResponse.json();
                        updateAIAnalysis(aiData);
                    }

                    // Fetch market data
                    const marketResponse = await fetch('/api/market-data');
                    if (marketResponse.ok) {
                        const marketData = await marketResponse.json();
                        updateMarketData(marketData);
                    }

                    // Fetch strategy status
                    const statusResponse = await fetch('/api/strategy/status');
                    if (statusResponse.ok) {
                        const statusData = await statusResponse.json();
                        updateStrategyStatus(statusData.strategy_status);
                    }

                } catch (error) {
                    console.error('Error updating data:', error);
                }
            }, 2000);

            // Update system health every 5 seconds
            setInterval(updateSystemHealth, 5000);
        }

        function updateUptime() {
            setInterval(function() {
                const now = Date.now();
                const uptime = now - startTime;
                const hours = Math.floor(uptime / (1000 * 60 * 60));
                const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
                document.getElementById('uptime').textContent = `${hours}h ${minutes}m`;
            }, 60000);
        }

        function formatLargeNumber(num) {
            if (num >= 1e9) return (num / 1e9).toFixed(1) + 'B';
            if (num >= 1e6) return (num / 1e6).toFixed(1) + 'M';
            if (num >= 1e3) return (num / 1e3).toFixed(1) + 'K';
            return num.toString();
        }
    </script>
</body>
</html>
        """

    async def websocket_handler(self, request):
        """Handle WebSocket connections."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websockets.add(ws)
        logger.info(f"📡 WebSocket connected (total: {len(self.websockets)})")

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # Handle incoming messages if needed
                    pass
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.websockets.discard(ws)
            logger.info(f"📡 WebSocket disconnected (total: {len(self.websockets)})")

        return ws

    async def api_market_data(self, request):
        """API endpoint for market data."""
        try:
            # Get latest market data from SQLite bus
            data = self.get_latest_market_data()
            return web.json_response(data)
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def api_signals(self, request):
        """API endpoint for trading signals."""
        try:
            signals = self.get_trading_signals()
            return web.json_response(signals)
        except Exception as e:
            logger.error(f"Error getting signals: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def api_trades(self, request):
        """API endpoint for trades."""
        try:
            trades = self.get_trades_data()
            return web.json_response(trades)
        except Exception as e:
            logger.error(f"Error getting trades: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def api_ai_analysis(self, request):
        """API endpoint for AI analysis."""
        try:
            analysis = self.get_ai_analysis()
            return web.json_response(analysis)
        except Exception as e:
            logger.error(f"Error getting AI analysis: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def api_strategy_status(self, request):
        """API endpoint for strategy status."""
        try:
            status = self.get_strategy_status()
            return web.json_response({
                "current_strategy": self.current_strategy,
                "available_strategies": self.available_strategies,
                "current_symbol": self.current_symbol,
                "available_symbols": self.available_symbols,
                "strategy_status": status
            })
        except Exception as e:
            logger.error(f"Error getting strategy status: {e}")
            return web.json_response({"error": str(e)}, status=500)

    async def api_strategy_start(self, request):
        """API endpoint to start a strategy."""
        try:
            data = await request.json()
            strategy = data.get('strategy', self.current_strategy)

            success = await self.start_strategy(strategy)

            if success:
                return web.json_response({
                    "success": True,
                    "message": f"Strategy {strategy} started successfully",
                    "strategy": strategy,
                    "status": self.get_strategy_status()
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": f"Failed to start strategy {strategy}"
                }, status=500)

        except Exception as e:
            logger.error(f"Error starting strategy: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)

    async def api_strategy_stop(self, request):
        """API endpoint to stop the current strategy."""
        try:
            success = await self.stop_strategy()

            if success:
                return web.json_response({
                    "success": True,
                    "message": "Strategy stopped successfully",
                    "status": self.get_strategy_status()
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": "Failed to stop strategy"
                }, status=500)

        except Exception as e:
            logger.error(f"Error stopping strategy: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)

    async def api_symbol_select(self, request):
        """API endpoint for symbol selection."""
        try:
            data = await request.json()
            symbol = data.get('symbol')

            if symbol in self.available_symbols:
                old_symbol = self.current_symbol
                self.current_symbol = symbol
                logger.info(f"🔁 Symbol changed from {old_symbol} → {symbol}")

                # Broadcast symbol change to all connected clients
                await self.broadcast_update("symbol_change", {
                    "old_symbol": old_symbol,
                    "new_symbol": symbol,
                    "message": f"Symbol changed to {symbol}"
                })

                return web.json_response({
                    "success": True,
                    "symbol": symbol,
                    "message": f"Symbol changed to {symbol}"
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": f"Invalid symbol: {symbol}"
                }, status=400)

        except Exception as e:
            logger.error(f"Error changing symbol: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)

    async def api_metrics_toggle(self, request):
        """API endpoint for metrics toggling."""
        try:
            data = await request.json()
            metric = data.get('metric')
            enabled = data.get('enabled', False)

            if metric in self.enabled_metrics:
                self.enabled_metrics[metric] = enabled
                logger.info(f"📊 Metric {metric} {'enabled' if enabled else 'disabled'}")

                return web.json_response({
                    "success": True,
                    "metric": metric,
                    "enabled": enabled,
                    "all_metrics": self.enabled_metrics
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": f"Invalid metric: {metric}"
                }, status=400)

        except Exception as e:
            logger.error(f"Error toggling metric: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)

    # Data handling methods
    def get_latest_market_data(self):
        """Get latest market data from SQLite bus."""
        try:
            # Check if database exists first
            if not os.path.exists(self.db_path):
                logger.warning(f"Database file does not exist: {self.db_path}")
                return {
                    "symbol": self.current_symbol,
                    "price": 50000,  # Default price
                    "change_24h": 0,
                    "volume_24h": 0,
                    "timestamp": time.time()
                }

            # Use timeout to prevent hanging
            conn = sqlite3.connect(self.db_path, timeout=5.0)
            cursor = conn.cursor()

            # Get latest kline data for current symbol
            cursor.execute('''
                SELECT payload FROM messages
                WHERE stream = ?
                ORDER BY ts DESC LIMIT 1
            ''', (f'kline.{self.current_symbol}',))

            result = cursor.fetchone()
            if result:
                data = json.loads(result[0])
                return {
                    "symbol": self.current_symbol,
                    "price": data.get('close', 50000),
                    "change_24h": data.get('change_24h', 0),
                    "volume_24h": data.get('volume', 0),
                    "timestamp": data.get('timestamp', time.time())
                }
            else:
                return {
                    "symbol": self.current_symbol,
                    "price": 50000,  # Default price
                    "change_24h": 0,
                    "volume_24h": 0,
                    "timestamp": time.time()
                }

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e).lower():
                logger.warning(f"Database is locked, using default market data")
                return {
                    "symbol": self.current_symbol,
                    "price": 50000,  # Default price
                    "change_24h": 0,
                    "volume_24h": 0,
                    "timestamp": time.time()
                }
            else:
                logger.error(f"Database error getting market data: {e}")
                return {"error": str(e)}
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return {"error": str(e)}
        finally:
            if 'conn' in locals():
                conn.close()

    def get_trading_signals(self):
        """Get recent trading signals from SQLite bus."""
        try:
            # Check if database exists first
            if not os.path.exists(self.db_path):
                logger.warning(f"Database file does not exist: {self.db_path}")
                return []

            # Use timeout to prevent hanging
            conn = sqlite3.connect(self.db_path, timeout=5.0)
            cursor = conn.cursor()

            # Get recent signals
            cursor.execute('''
                SELECT stream, payload, ts FROM messages
                WHERE stream LIKE 'signals.%'
                ORDER BY ts DESC LIMIT 10
            ''')

            signals = []
            for row in cursor.fetchall():
                stream, payload, ts = row
                try:
                    data = json.loads(payload)
                    signals.append({
                        "stream": stream,
                        "data": data,
                        "timestamp": ts
                    })
                except:
                    continue

            return signals

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e).lower():
                logger.warning(f"Database is locked, skipping signal collection")
                return []
            else:
                logger.error(f"Database error getting signals: {e}")
                return []
        except Exception as e:
            logger.error(f"Error getting signals: {e}")
            return []
        finally:
            if 'conn' in locals():
                conn.close()

    def get_trades_data(self):
        """Get trades data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get recent trade data
            cursor.execute('''
                SELECT payload FROM messages
                WHERE stream LIKE 'trades.%'
                ORDER BY ts DESC LIMIT 20
            ''')

            trades = []
            for row in cursor.fetchall():
                try:
                    data = json.loads(row[0])
                    trades.append(data)
                except:
                    continue

            return {
                "active_trades": [],
                "trade_history": trades
            }

        except Exception as e:
            logger.error(f"Error getting trades: {e}")
            return {"active_trades": [], "trade_history": []}
        finally:
            if 'conn' in locals():
                conn.close()

    def get_ai_analysis(self):
        """Get AI analysis data from signals."""
        try:
            # Check if database exists first
            if not os.path.exists(self.db_path):
                logger.warning(f"Database file does not exist: {self.db_path}")
                return {
                    "rsi": 50,
                    "trend": "Neutral",
                    "volatility": "Low",
                    "signal_strength": 0,
                    "direction": "HOLD",
                    "timestamp": time.time()
                }

            # Use timeout to prevent hanging
            conn = sqlite3.connect(self.db_path, timeout=5.0)
            cursor = conn.cursor()

            # Get latest LLM or fused signals
            cursor.execute('''
                SELECT payload FROM messages
                WHERE stream IN ('signals.llm', 'signals.fused', 'signals.ensemble')
                ORDER BY ts DESC LIMIT 1
            ''')

            result = cursor.fetchone()
            if result:
                data = json.loads(result[0])

                # Extract AI analysis metrics
                analysis = {
                    "rsi": data.get('rsi', 50),
                    "trend": data.get('trend', 'Neutral'),
                    "volatility": data.get('volatility', 'Medium'),
                    "signal_strength": data.get('score', data.get('confidence', 0.5)),
                    "direction": data.get('action', data.get('direction', 'HOLD')),
                    "timestamp": data.get('timestamp', time.time())
                }

                return analysis
            else:
                return {
                    "rsi": 50,
                    "trend": "Neutral",
                    "volatility": "Low",
                    "signal_strength": 0,
                    "direction": "HOLD",
                    "timestamp": time.time()
                }

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e).lower():
                logger.warning(f"Database is locked, using default AI analysis")
                return {
                    "rsi": 50,
                    "trend": "Neutral",
                    "volatility": "Low",
                    "signal_strength": 0,
                    "direction": "HOLD",
                    "timestamp": time.time()
                }
            else:
                logger.error(f"Database error getting AI analysis: {e}")
                return {"error": str(e)}
        except Exception as e:
            logger.error(f"Error getting AI analysis: {e}")
            return {"error": str(e)}
        finally:
            if 'conn' in locals():
                conn.close()

    def get_strategy_status(self):
        """Get current strategy status."""
        # Check if current strategy process is actually running
        current_strategy_running = False
        if self.current_strategy in self.strategy_processes:
            proc = self.strategy_processes[self.current_strategy]
            current_strategy_running = proc.poll() is None

        # Update internal state to match reality
        self.strategy_running = current_strategy_running

        # Count all active processes
        active_processes = [p for p in self.strategy_processes.values() if p.poll() is None]

        # Get strategy stats
        stats = self.strategy_stats.get(self.current_strategy, {
            "signals_generated": 0,
            "avg_confidence": 0.0,
            "last_signal_time": None,
            "status": "stopped",
            "uptime": 0
        })

        return {
            "current_strategy": self.current_strategy,
            "strategy_running": current_strategy_running,
            "active_processes": len(active_processes),
            "total_processes": len(self.strategy_processes),
            "signals_generated": stats["signals_generated"],
            "avg_confidence": stats["avg_confidence"],
            "last_signal_time": stats["last_signal_time"],
            "uptime": stats["uptime"]
        }

    async def start_strategy(self, strategy_name: str) -> bool:
        """Start a specific strategy."""
        try:
            if strategy_name not in self.strategy_commands:
                logger.error(f"Unknown strategy: {strategy_name}")
                return False

            # Stop current strategy if running
            if self.strategy_running:
                logger.info(f"Stopping current strategy '{self.current_strategy}' before starting '{strategy_name}'")
                await self.stop_strategy()

            # Clean up any existing dead processes for this strategy
            if strategy_name in self.strategy_processes:
                old_proc = self.strategy_processes[strategy_name]
                if old_proc.poll() is not None:
                    logger.info(f"Cleaning up dead process for strategy '{strategy_name}'")
                    del self.strategy_processes[strategy_name]

            # Start new strategy
            command = self.strategy_commands[strategy_name]
            logger.info(f"Starting strategy '{strategy_name}' with command: {command}")

            proc = subprocess.Popen(
                command.split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=None  # Use current working directory
            )

            # Wait a moment to check if process started successfully
            await asyncio.sleep(0.5)
            if proc.poll() is not None:
                # Process died immediately, get error output
                stdout, stderr = proc.communicate()
                logger.error(f"Strategy '{strategy_name}' failed to start. Return code: {proc.returncode}")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False

            self.strategy_processes[strategy_name] = proc
            self.current_strategy = strategy_name
            self.strategy_running = True

            # Update strategy stats
            self.strategy_stats[strategy_name]["status"] = "running"
            self.strategy_stats[strategy_name]["uptime"] = time.time()

            logger.info(f"✅ Started strategy '{strategy_name}' with PID: {proc.pid}")
            return True

        except Exception as e:
            logger.error(f"Failed to start strategy '{strategy_name}': {e}")
            return False

    async def stop_strategy(self) -> bool:
        """Stop the current strategy."""
        try:
            if not self.strategy_running:
                return True

            # Stop current strategy process
            if self.current_strategy in self.strategy_processes:
                proc = self.strategy_processes[self.current_strategy]
                if proc.poll() is None:
                    proc.terminate()
                    try:
                        proc.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        proc.kill()
                    logger.info(f"✅ Stopped strategy '{self.current_strategy}'")

                # Update strategy stats
                self.strategy_stats[self.current_strategy]["status"] = "stopped"

            self.strategy_running = False
            return True

        except Exception as e:
            logger.error(f"Failed to stop strategy: {e}")
            return False

    async def broadcast_update(self, update_type: str, data: Dict[str, Any]):
        """Broadcast update to all connected WebSocket clients."""
        if not self.websockets:
            return

        message = {
            "type": update_type,
            "data": data,
            "timestamp": time.time()
        }

        # Send to all connected clients
        disconnected = set()
        for ws in self.websockets:
            try:
                await ws.send_str(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending WebSocket message: {e}")
                disconnected.add(ws)

        # Remove disconnected clients
        self.websockets -= disconnected

        if disconnected:
            logger.info(f"📡 Removed {len(disconnected)} disconnected WebSocket clients")

    async def background_updater(self):
        """Background task to update data and broadcast to WebSockets."""
        logger.info("🔄 Background updater started")
        update_count = 0

        while True:
            try:
                update_count += 1

                # Log every 20th update to confirm it's running
                if update_count % 20 == 0:
                    logger.info(f"🔄 Background updater running - Update #{update_count}")

                # Get latest data
                market_data = self.get_latest_market_data()
                ai_analysis = self.get_ai_analysis()
                strategy_status = self.get_strategy_status()

                # Broadcast updates to WebSocket clients
                if self.websockets:
                    await self.broadcast_update("market_update", market_data)
                    await self.broadcast_update("ai_analysis", ai_analysis)
                    await self.broadcast_update("strategy_status", strategy_status)

                # Cache the data
                self.data_cache.update({
                    "market_data": market_data,
                    "ai_analysis": ai_analysis,
                    "strategy_status": strategy_status,
                    "last_update": time.time()
                })

            except Exception as e:
                logger.error(f"❌ Error in background updater: {e}")

            await asyncio.sleep(2)  # Update every 2 seconds

    async def data_collector(self):
        """Background task to collect and process data from SQLite bus."""
        logger.info("📊 Data collector started")

        while True:
            try:
                # Update strategy performance stats
                signals = self.get_trading_signals()
                for signal in signals:
                    self._update_strategy_performance(signal.get('data', {}))

            except Exception as e:
                logger.error(f"❌ Error in data collector: {e}")

            await asyncio.sleep(5)  # Collect every 5 seconds

    def _update_strategy_performance(self, signal: Dict[str, Any]):
        """Update strategy-specific performance metrics."""
        try:
            # Extract strategy source from signal
            source = signal.get('source', '')
            rationale = signal.get('rationale', '')

            # Map signal sources to strategy names
            strategy_mapping = {
                'smart': 'Smart Model Integrated',
                'rsi': 'RSI Strategy',
                'bollinger': 'Bollinger Bands',
                'vwap': 'VWAP Strategy',
                'multi': 'Multi-Signal'
            }

            # Determine strategy from signal
            strategy_name = None
            for key, name in strategy_mapping.items():
                if key in source.lower() or key in rationale.lower():
                    strategy_name = name
                    break

            if not strategy_name:
                strategy_name = self.current_strategy  # Fallback to current strategy

            # Update strategy stats
            if strategy_name in self.strategy_stats:
                stats = self.strategy_stats[strategy_name]
                stats['signals_generated'] += 1
                stats['last_signal_time'] = datetime.now().isoformat()

                # Update confidence tracking
                try:
                    confidence = float(signal.get('score', signal.get('confidence', 0)))
                    if stats['signals_generated'] == 1:
                        stats['avg_confidence'] = confidence
                    else:
                        # Running average
                        stats['avg_confidence'] = (stats['avg_confidence'] * (stats['signals_generated'] - 1) + confidence) / stats['signals_generated']
                except (ValueError, TypeError):
                    pass

                logger.info(f"📊 Updated {strategy_name} stats: {stats['signals_generated']} signals")

        except Exception as e:
            logger.error(f"❌ Error updating strategy performance: {e}")

    async def run(self):
        """Run the beautiful dashboard server."""
        await self.init_app()

        runner = web.AppRunner(self.app)
        await runner.setup()

        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()

        logger.info(f"🌟 Beautiful Dashboard running on http://localhost:{self.port}")
        logger.info(f"🔐 Login with username: epinnox, password: securepass123")

        # Start background tasks after server is running
        await self.start_background_tasks()

        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Shutting down Beautiful Dashboard...")
        finally:
            await runner.cleanup()


async def main():
    """Main function to run the beautiful dashboard."""
    dashboard = BeautifulDashboard()
    await dashboard.run()


if __name__ == "__main__":
    asyncio.run(main())