#!/usr/bin/env python3
"""
Simple synchronous test script for strategy operations and monitoring metrics.
Uses requests library to avoid Windows event loop issues.
"""

import requests
import json
import time
import sys

class SimpleStrategyTester:
    def __init__(self, base_url="http://localhost:8082"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def login(self):
        """Login to get authenticated session."""
        login_data = {
            "username": "epinnox",
            "password": "securepass123"
        }
        
        response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code == 302:  # Redirect after successful login
            print("✅ Authentication successful")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            return False
    
    def test_endpoint(self, endpoint, method="GET", data=None, expected_status=200):
        """Test a specific API endpoint."""
        try:
            if method == "GET":
                response = self.session.get(f"{self.base_url}{endpoint}")
            elif method == "POST":
                response = self.session.post(f"{self.base_url}{endpoint}", json=data)
            
            success = response.status_code == expected_status
            
            try:
                content = response.json()
            except:
                content = response.text
            
            result = {
                "endpoint": endpoint,
                "method": method,
                "status": response.status_code,
                "expected": expected_status,
                "success": success,
                "content": content if success else f"Error: {response.status_code}"
            }
            
            self.test_results.append(result)
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} {method} {endpoint} - Status: {response.status_code}")
            
            return content if success else None
            
        except Exception as e:
            print(f"❌ {method} {endpoint} - Exception: {e}")
            self.test_results.append({
                "endpoint": endpoint,
                "method": method,
                "success": False,
                "error": str(e)
            })
            return None
    
    def test_strategy_lifecycle(self, strategy_name):
        """Test complete strategy lifecycle: start -> verify -> stop."""
        print(f"\n🔄 Testing strategy lifecycle for: {strategy_name}")
        
        # 1. Get initial status
        initial_status = self.test_endpoint("/api/strategy/status")
        if not initial_status:
            return False
        
        print(f"   Initial status: {initial_status['strategy_status']['strategy_running']}")
        
        # 2. Start strategy
        start_result = self.test_endpoint(
            "/api/strategy/start", 
            method="POST", 
            data={"strategy": strategy_name}
        )
        
        if not start_result or not start_result.get('success'):
            print(f"   ❌ Failed to start {strategy_name}")
            if start_result:
                print(f"      Error: {start_result.get('error', 'Unknown error')}")
            return False
        
        print(f"   ✅ Started {strategy_name}")
        
        # 3. Wait and verify it's running
        time.sleep(3)
        running_status = self.test_endpoint("/api/strategy/status")
        
        if running_status and running_status['strategy_status']['strategy_running']:
            print(f"   ✅ Verified {strategy_name} is running")
            
            # Show process details
            process_details = running_status['strategy_status'].get('process_details', {})
            if strategy_name in process_details:
                pid = process_details[strategy_name].get('pid')
                print(f"      PID: {pid}")
            
            # 4. Stop strategy
            stop_result = self.test_endpoint("/api/strategy/stop", method="POST")
            
            if stop_result and stop_result.get('success'):
                print(f"   ✅ Stopped {strategy_name}")
                
                # 5. Verify it's stopped
                time.sleep(2)
                stopped_status = self.test_endpoint("/api/strategy/status")
                
                if stopped_status and not stopped_status['strategy_status']['strategy_running']:
                    print(f"   ✅ Verified {strategy_name} is stopped")
                    return True
                else:
                    print(f"   ❌ {strategy_name} still appears to be running")
                    return False
            else:
                print(f"   ❌ Failed to stop {strategy_name}")
                if stop_result:
                    print(f"      Error: {stop_result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ {strategy_name} not running after start")
            return False
    
    def test_metrics_system(self):
        """Test monitoring metrics toggling."""
        print(f"\n📊 Testing metrics system")
        
        # Get current metrics status
        status = self.test_endpoint("/api/strategy/status")
        if not status:
            return False
        
        current_metrics = status.get('enabled_metrics', {})
        print(f"   Current metrics: {list(current_metrics.keys())}")
        
        # Test toggling a few key metrics
        test_metrics = ['rsi', 'vwap', 'volume_spike']
        
        for metric in test_metrics:
            if metric in current_metrics:
                current_state = current_metrics[metric]
                
                # Toggle to opposite state
                toggle_result = self.test_endpoint(
                    "/api/metrics/toggle",
                    method="POST",
                    data={"metric": metric, "enabled": not current_state}
                )
                
                if toggle_result and toggle_result.get('success'):
                    print(f"   ✅ Toggled {metric}: {current_state} → {not current_state}")
                    
                    # Toggle back to original state
                    self.test_endpoint(
                        "/api/metrics/toggle",
                        method="POST", 
                        data={"metric": metric, "enabled": current_state}
                    )
                    print(f"   ✅ Restored {metric} to original state: {current_state}")
                else:
                    print(f"   ❌ Failed to toggle {metric}")
        
        return True
    
    def test_all_api_endpoints(self):
        """Test all API endpoints for basic functionality."""
        print(f"\n🌐 Testing all API endpoints")
        
        endpoints = [
            "/api/market-data",
            "/api/signals", 
            "/api/trades",
            "/api/stats",
            "/api/orderbook",
            "/api/recent-trades",
            "/api/ai-analysis",
            "/api/market-sentiment",
            "/api/debug",
            "/api/strategy/status"
        ]
        
        for endpoint in endpoints:
            self.test_endpoint(endpoint)
        
        return True
    
    def run_comprehensive_test(self):
        """Run all tests in sequence."""
        print("🚀 Starting comprehensive strategy operations test")
        print("=" * 60)
        
        # Setup
        if not self.login():
            return False
        
        # Test all API endpoints
        self.test_all_api_endpoints()
        
        # Test metrics system
        self.test_metrics_system()
        
        # Test strategy lifecycle for key strategies
        strategies_to_test = [
            "Smart Model Integrated",
            "RSI Strategy", 
            "Bollinger Bands"
        ]
        
        for strategy in strategies_to_test:
            success = self.test_strategy_lifecycle(strategy)
            if not success:
                print(f"⚠️  Strategy {strategy} lifecycle test failed")
        
        # Generate report
        self.generate_report()
        
        return True
    
    def generate_report(self):
        """Generate test results report."""
        print("\n" + "=" * 60)
        print("📋 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r.get('success', False)])
        
        print(f"Total Tests: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {total_tests - successful_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [r for r in self.test_results if not r.get('success', False)]
        if failed_tests:
            print(f"\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"   - {test['method']} {test['endpoint']}: {test.get('error', 'Unknown error')}")
        else:
            print(f"\n🎉 All tests passed!")

def main():
    """Main test runner."""
    tester = SimpleStrategyTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
